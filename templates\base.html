<!DOCTYPE html>
<html lang="en" style="overflow-y: scroll;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>{% block title %}<span data-i18n="general.site_title">ilovetifinagh.com</span>{% endblock %}</title>
    <!-- CSS Reset -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/reset.css') }}?v=1.0.1">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <!-- Layout CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/layout.css') }}?v=1.0.1">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/new-style.css') }}?v=1.0.1">
    <!-- Editors CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/editors.css') }}?v=1.0.1">
    <!-- Footer CSS (Combined footer-fix.css and footer-enhancements.css) -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/footer-fix.css') }}?v=1.0.2">
    <!-- Copyright Year Fix CSS - تحسينات مظهر السنة في حقوق النشر -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/copyright-year-fix.css') }}?v=1.0.0">
    <!-- Scrollbar Fix CSS - شريط التمرير الداخلي للمحررين -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/scrollbar-fix.css') }}?v=7.0.0">
    <!-- Single Scrollbar Only CSS - ضمان شريط تمرير واحد فقط -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/single-scrollbar-only.css') }}?v=5.0.0">
    <!-- Simple Editor Scrollbar CSS - ضمان شريط التمرير في المحرر البسيط -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/simple-editor-scrollbar.css') }}?v=3.0.0">
    <!-- Editor Scrollbar Fix CSS - ملف مهجور للتوافق -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/fix-editor-scrollbar.css') }}?v=2.0.0">
    <!-- Simple Editor Scrollbar Force CSS - أولوية قصوى لإجبار ظهور شريط التمرير -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/simple-editor-scrollbar-force.css') }}?v=1.0.0">
    <!-- Dropdown Z-Index Fix CSS - إصلاح مشكلة z-index للقوائم المنسدلة -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dropdown-z-index-fix.css') }}?v=1.0.0">
    <!-- Dynamic CSS from settings -->
    <link rel="stylesheet" href="{{ url_for('dynamic_css') }}">
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Tifinagh&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- تحميل الخطوط مع display=swap لتجنب تأخير عرض النص -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri&family=Open+Sans&family=Roboto&family=Scheherazade+New&display=swap" rel="stylesheet">
    <!-- تحميل خط تيفيناغ محليًا لتجنب التأخير -->
    <style>
        @font-face {
            font-family: 'Noto Sans Tifinagh';
            font-style: normal;
            font-weight: 400;
            font-display: swap;
            src: url('/static/fonts/NotoSansTifinagh-Regular.woff2') format('woff2'),
                 url('/static/fonts/NotoSansTifinagh-Regular.woff') format('woff');
        }

        /* منع تمدد النص عند تحميل الخطوط */
        .font-loaded {
            transition: none;
        }

        /* تطبيق الخطوط فقط بعد تحميلها */
        .fonts-loading .text-area-tifinagh {
            visibility: hidden;
        }

        .fonts-ready .text-area-tifinagh {
            visibility: visible;
        }

        /* إصلاح لون أيقونة البريد الإلكتروني */
        .social-icon .bi-envelope,
        .social-icon .email-icon,
        .bi-envelope.text-white,
        .bi-envelope.email-icon,
        footer .social-icons a[href^="mailto:"] i {
            color: #ffffff !important;
        }
    </style>
    <!-- سكريبت للتحقق من تحميل الخطوط -->
    <script>
        document.documentElement.classList.add('fonts-loading');

        // التحقق من تحميل الخطوط بطريقة بسيطة
        function checkFontsLoaded() {
            // انتظار قصير للسماح للخطوط بالتحميل
            setTimeout(() => {
                document.documentElement.classList.remove('fonts-loading');
                document.documentElement.classList.add('fonts-ready');
            }, 1000);
        }

        // تشغيل فحص الخطوط عند تحميل الصفحة
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', checkFontsLoaded);
        } else {
            checkFontsLoaded();
        }
    </script>
    {% block extra_css %}{% endblock %}
</head>
<body class="d-flex flex-column min-vh-100">
    <header class="bg-white shadow-sm mb-4">
        <nav class="navbar navbar-expand-lg navbar-light py-3">
            <div class="container">
                <a class="navbar-brand py-0" href="{{ url_for('index') }}">
                    <img src="{{ url_for('static', filename='images/tifinagh-logo.svg') }}" alt="Tifinagh Converter Logo" height="60" class="d-block">
                </a>

                <!-- قائمة تغيير اللغة المحسنة -->
                <div class="language-selector ms-auto">
                    <div class="dropdown">
                        <button class="btn btn-outline-primary rounded-pill dropdown-toggle" type="button" id="languageDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-globe2 me-1"></i>
                            <span class="current-language">
                                {% if session.lang == 'am' %}
                                <span class="tifinagh-text" data-i18n="general.language_amazigh">ⵜⴰⵎⴰⵣⵉⵖⵜ</span>
                                {% else %}
                                <span data-i18n="general.language_english">English</span>
                                {% endif %}
                            </span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end shadow-sm border-0" aria-labelledby="languageDropdown">
                            {% if settings.enable_english %}
                            <li>
                                <button class="dropdown-item lang-btn d-flex align-items-center {% if session.lang == 'en' %}active{% endif %}" type="button" data-lang="en">
                                    <i class="bi bi-check2 me-2 {% if session.lang != 'en' %}invisible{% endif %}"></i>
                                    <span data-i18n="common.english">English</span>
                                </button>
                            </li>
                            {% endif %}
                            {% if settings.enable_amazigh %}
                            <li>
                                <button class="dropdown-item lang-btn d-flex align-items-center {% if session.lang == 'am' %}active{% endif %}" type="button" data-lang="am">
                                    <i class="bi bi-check2 me-2 {% if session.lang != 'am' %}invisible{% endif %}"></i>
                                    <span class="tifinagh-text" data-i18n="common.amazigh">ⵜⴰⵎⴰⵣⵉⵖⵜ</span>
                                </button>
                            </li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <main class="main-content">
        {% block content %}{% endblock %}
    </main>

    {% if settings.show_footer %}
    <footer class="bg-dark text-white py-5 mt-0" style="min-height: 120px;">
        <div class="container">
            <div class="row align-items-center">
                <!-- Left Column: Site Info -->
                <div class="col-lg-4 col-md-12 text-center text-lg-start mb-4 mb-lg-0">
                    <div class="fw-bold" style="font-size:1.25rem; font-family: 'Inter', sans-serif; letter-spacing: -0.3px; margin-bottom: 10px;" data-i18n="common.app_name">Tifinagh Converter</div>
                    <hr class="my-2 border-light opacity-50">
                    <div style="font-size:0.9rem; font-family: 'Inter', sans-serif; line-height: 1.4; margin-top: 10px;" data-i18n="common.app_description">A tool to convert Latin script to Tifinagh script</div>
                </div>

                <!-- Center Column: Copyright -->
                <div class="col-lg-4 col-md-12 text-center mb-4 mb-lg-0">
                    <div style="font-size:0.9rem; font-family: 'Inter', sans-serif; margin-bottom: 12px;" data-i18n="footer.copyright">© <span id="current-year"></span> Tifinagh Converter. All rights reserved.</div>
                    <div style="font-size:0.85rem; font-family: 'Inter', sans-serif; color: #e2e8f0; margin-top: 12px;" data-i18n="footer.designed_by">Designed by Mohammed RACHIDI</div>
                </div>

                <!-- Right Column: Social Media -->
                <div class="col-lg-4 col-md-12 text-center">
                    <div style="font-size:0.9rem; font-family: 'Inter', sans-serif; margin-bottom: 10px;" data-i18n="footer.connect_with_us">Connect with us</div>
                    <div class="social-icons">
                        {% if g.active_social_media %}
                            {% for site_id, site_data in g.active_social_media.items() %}
                                {% if site_id == 'email' %}
                                    <a href="mailto:{{ site_data.url }}" class="social-icon" title="{{ site_data.name }}">
                                        <i class="bi {{ site_data.icon }} text-white email-icon"></i>
                                    </a>
                                {% else %}
                                    <a href="{{ site_data.url }}" class="social-icon" title="{{ site_data.name }}" target="_blank">
                                        <i class="bi {{ site_data.icon }}"></i>
                                    </a>
                                {% endif %}
                            {% endfor %}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </footer>
    {% endif %}

    <script>
        // تحديث السنة الحالية في حقوق النشر - يعمل بعد تحميل نظام الترجمة
        function updateCopyrightYear() {
            const yearElement = document.getElementById('current-year');
            if (yearElement) {
                yearElement.textContent = new Date().getFullYear();
                console.log('Copyright year updated to:', new Date().getFullYear());
            }
        }

        // تنفيذ فوري للسنة
        document.addEventListener('DOMContentLoaded', function() {
            updateCopyrightYear();
        });

        // تنفيذ بعد تحميل نظام الترجمة
        document.addEventListener('languageSystemLoaded', function() {
            setTimeout(updateCopyrightYear, 100);
        });

        // تنفيذ بعد تغيير اللغة
        document.addEventListener('language:changed', function() {
            setTimeout(updateCopyrightYear, 100);
        });
    </script>

    <!-- تم إزالة كود JavaScript الخاص بالروابط لأننا نستخدم الآن الروابط المباشرة -->

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>

    <!-- Debug Script - Load First -->
    <script>
        console.log('🔧 Starting JavaScript loading sequence...');
        window.jsLoadingDebug = {
            loadedScripts: [],
            errors: [],
            log: function(message) {
                console.log('[JS Debug] ' + message);
                this.loadedScripts.push(message);
            },
            error: function(message) {
                console.error('[JS Debug] ' + message);
                this.errors.push(message);
            }
        };
        window.jsLoadingDebug.log('Debug system initialized');
    </script>

    <!-- New Language System - Clean & Efficient v2.0.0 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/language-system-new.css') }}?v=2.0.0">
    <script src="{{ url_for('static', filename='js/language-system-new.js') }}?v=2.0.0" onload="window.jsLoadingDebug.log('New language system loaded successfully')" onerror="window.jsLoadingDebug.error('New language system failed to load')"></script>
    <!-- Compatibility Fix for Legacy Code -->
    <script src="{{ url_for('static', filename='js/compatibility-fix.js') }}?v=1.1.0" onload="window.jsLoadingDebug.log('Compatibility fix loaded successfully')" onerror="window.jsLoadingDebug.error('Compatibility fix failed to load')"></script>

    <!-- Legacy Language System (Backup) - سيتم إزالته لاحقاً -->
    <!-- <script src="{{ url_for('static', filename='js/i18n.js') }}?v=4.0.0"></script> -->
    <!-- <script src="{{ url_for('static', filename='js/language-switcher.js') }}?v=4.0.0"></script> -->
    <!-- <script src="{{ url_for('static', filename='js/dropdown-fix.js') }}?v=1.0.0"></script> -->
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}?v=4.0.0" onload="window.jsLoadingDebug.log('main.js loaded successfully')" onerror="window.jsLoadingDebug.error('main.js failed to load')"></script>

    {% if policies.show_consent_banner %}
    <!-- شريط موافقة ملفات تعريف الارتباط -->
    <div id="cookie-consent-banner" class="cookie-banner {{ policies.banner_position }} {{ policies.banner_theme }}">
        <h5>{{ policies.consent_title }}</h5>
        <p>{{ policies.consent_message }}</p>
        <div class="cookie-buttons">
            <button id="accept-cookies" class="btn btn-primary">{{ policies.accept_button_text }}</button>
            <button id="reject-cookies" class="btn btn-outline-secondary">{{ policies.reject_button_text }}</button>
            {% if policies.cookie_enabled %}
            <a href="{{ url_for('cookie_policy') }}" class="btn btn-link">{{ policies.settings_button_text }}</a>
            {% endif %}
        </div>
    </div>

    <script>
        // التحقق مما إذا كان المستخدم قد وافق على ملفات تعريف الارتباط من قبل
        document.addEventListener('DOMContentLoaded', function() {
            const cookieConsent = localStorage.getItem('cookie-consent');
            const cookieBanner = document.getElementById('cookie-consent-banner');

            if (!cookieConsent && cookieBanner) {
                cookieBanner.style.display = 'block';

                // زر قبول ملفات تعريف الارتباط
                document.getElementById('accept-cookies').addEventListener('click', function() {
                    localStorage.setItem('cookie-consent', 'accepted');
                    cookieBanner.style.display = 'none';
                });

                // زر رفض ملفات تعريف الارتباط
                document.getElementById('reject-cookies').addEventListener('click', function() {
                    localStorage.setItem('cookie-consent', 'rejected');
                    cookieBanner.style.display = 'none';
                });
            } else if (cookieBanner) {
                cookieBanner.style.display = 'none';
            }
        });
    </script>
    {% endif %}

    <!-- Fix editor height and scrollbar scripts -->
    <script src="{{ url_for('static', filename='js/fix-editor-height.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='js/scrollbar-fix.js') }}?v=6.0.0"></script>
    <!-- Simple Editor Fix - إصلاح مشاكل المحرر البسيط -->
    <script src="{{ url_for('static', filename='js/simple-editor-fix.js') }}?v=2.0.0"></script>
    <!-- Copyright Year Fix - إصلاح مشكلة عرض السنة في حقوق النشر -->
    <script src="{{ url_for('static', filename='js/copyright-year-fix.js') }}?v=1.0.0"></script>
    <!-- HTML Entities Fix - إصلاح مشكلة HTML entities في النصوص المترجمة -->
    <script src="{{ url_for('static', filename='js/html-entities-fix.js') }}?v=1.0.0"></script>

    <!-- Final Debug Check -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                console.log('🔧 Final JavaScript Debug Check:');
                console.log('Loaded scripts:', window.jsLoadingDebug.loadedScripts);
                console.log('Errors:', window.jsLoadingDebug.errors);

                // Check global objects
                console.log('window.i18n exists:', typeof window.i18n !== 'undefined');
                console.log('window.languageSwitcher exists:', typeof window.languageSwitcher !== 'undefined');
                console.log('window.switchToEnglish exists:', typeof window.switchToEnglish === 'function');
                console.log('window.switchToAmazigh exists:', typeof window.switchToAmazigh === 'function');

                // Check language buttons
                const langButtons = document.querySelectorAll('.lang-btn');
                console.log('Language buttons found:', langButtons.length);

                if (typeof window.i18n !== 'undefined') {
                    console.log('i18n current language:', window.i18n.currentLang);
                    console.log('i18n is loaded:', window.i18n.isLoaded);
                }

                if (typeof window.languageSwitcher !== 'undefined') {
                    console.log('Language switcher initialized:', window.languageSwitcher.isInitialized);
                    console.log('Language switcher ready:', window.languageSwitcher.isReady());
                }

                // Test language switching
                if (typeof window.switchToAmazigh === 'function') {
                    console.log('🧪 Testing language switch to Amazigh...');
                    try {
                        const result = window.switchToAmazigh();
                        console.log('Switch result:', result);
                    } catch (error) {
                        console.error('Switch test failed:', error);
                    }
                }
            }, 2000); // Wait 2 seconds for everything to load
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>

