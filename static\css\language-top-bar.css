/**
 * منتقي اللغة في الهيدر - تصميم أنيق ومتطور
 * Header Language Selector - Elegant and Modern Design
 */

/* حاوي منتقي اللغة في الهيدر */
.language-selector-header {
    position: relative;
    display: inline-block;
}

/* زر القائمة المنسدلة المحسن في الهيدر */
.language-dropdown-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    border: 2px solid #667eea;
    color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.05) 100%);
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1),
                0 2px 4px rgba(102, 126, 234, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
    outline: none;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    letter-spacing: 0.3px;
}

/* تأثير الإضاءة الداخلية */
.language-dropdown-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.language-dropdown-btn:hover::before {
    left: 100%;
}

.language-dropdown-btn:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.15) 100%);
    border-color: #5a67d8;
    color: #5a67d8;
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2),
                0 4px 10px rgba(102, 126, 234, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.language-dropdown-btn:active {
    transform: translateY(-1px) scale(1.01);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.25) 0%, rgba(118, 75, 162, 0.2) 100%);
}

.language-dropdown-btn:focus {
    outline: none;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2),
                0 4px 10px rgba(102, 126, 234, 0.1),
                0 0 0 3px rgba(102, 126, 234, 0.3);
}

/* تحسين الأيقونة */
.language-dropdown-btn i {
    font-size: 1.1rem;
    margin-right: 8px;
    opacity: 0.8;
    transition: all 0.3s ease;
    color: inherit;
}

.language-dropdown-btn:hover i {
    opacity: 1;
    transform: rotate(15deg) scale(1.1);
    color: #5a67d8;
}

/* القائمة المنسدلة المحسنة */
.language-dropdown-menu {
    position: fixed; /* استخدام fixed بدلاً من absolute لتجنب مشاكل التخطيط */
    right: 20px; /* مسافة من الحافة اليمنى */
    top: 80px; /* مسافة ثابتة من الأعلى لتناسب الهيدر */
    z-index: 9999; /* z-index عالي جداً لضمان الظهور فوق كل شيء */
    min-width: 200px;
    max-width: 250px;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.1),
                0 10px 20px rgba(102, 126, 234, 0.05),
                0 0 0 1px rgba(102, 126, 234, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.15);
    list-style: none;
    margin: 0;
    padding: 8px 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-20px) scale(0.95);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    overflow: hidden;
}

.language-dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

/* تأثير الظهور المتدرج - السهم المؤشر */
.language-dropdown-menu::before {
    content: '';
    position: absolute;
    top: -10px;
    right: 30px;
    width: 20px;
    height: 20px;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid rgba(102, 126, 234, 0.15);
    border-bottom: none;
    border-right: none;
    transform: rotate(45deg);
    z-index: -1;
    box-shadow: -2px -2px 5px rgba(102, 126, 234, 0.05);
}

/* عناصر القائمة المحسنة */
.language-dropdown-menu li {
    margin: 2px 8px;
    padding: 0;
}

.lang-btn-new {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 12px 16px;
    font-size: 0.9rem;
    color: #374151;
    background: none;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.lang-btn-new::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    transition: left 0.3s ease;
}

.lang-btn-new:hover::before {
    left: 100%;
}

.lang-btn-new:hover {
    background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
    color: #4338ca;
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.lang-btn-new.active-lang-item {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.lang-btn-new.active-lang-item:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateX(2px);
}

/* تحسين الأيقونات في القائمة */
.lang-btn-new i {
    font-size: 1rem;
    margin-right: 12px;
    transition: all 0.2s ease;
    opacity: 0.7;
}

.lang-btn-new:hover i,
.lang-btn-new.active-lang-item i {
    opacity: 1;
    transform: scale(1.1);
}

.lang-btn-new.active-lang-item i {
    color: #fbbf24;
}

/* النص الأمازيغي */
.tifinagh-text {
    font-family: 'Noto Sans Tifinagh', sans-serif;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .language-dropdown-btn {
        padding: 8px 16px;
        font-size: 0.85rem;
        border-radius: 20px;
    }

    .language-dropdown-btn i {
        font-size: 1rem;
        margin-right: 6px;
    }

    .language-dropdown-menu {
        min-width: 180px;
        max-width: 200px;
        right: 15px !important;
        top: 70px !important;
        border-radius: 12px;
    }

    .lang-btn-new {
        padding: 10px 14px;
        font-size: 0.85rem;
        border-radius: 10px;
    }

    .lang-btn-new i {
        font-size: 0.9rem;
        margin-right: 10px;
    }
}

@media (max-width: 480px) {
    .language-dropdown-btn {
        padding: 6px 12px;
        font-size: 0.8rem;
        border-radius: 18px;
    }

    .language-dropdown-btn i {
        font-size: 0.9rem;
        margin-right: 5px;
    }

    .language-dropdown-menu {
        min-width: 160px;
        max-width: 180px;
        right: 10px !important;
        top: 65px !important;
        border-radius: 10px;
    }

    .lang-btn-new {
        padding: 8px 12px;
        font-size: 0.8rem;
        border-radius: 8px;
    }

    .lang-btn-new i {
        font-size: 0.85rem;
        margin-right: 8px;
    }

    /* تقليل حجم السهم المؤشر */
    .language-dropdown-menu::before {
        width: 15px;
        height: 15px;
        right: 25px;
        top: -8px;
    }
}

/* تحسين للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .language-dropdown-btn {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.1) 100%);
        border-color: #667eea;
        color: #a5b4fc;
    }

    .language-dropdown-btn:hover {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.25) 0%, rgba(118, 75, 162, 0.2) 100%);
        border-color: #818cf8;
        color: #c7d2fe;
    }

    .language-dropdown-btn:focus {
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3),
                    0 4px 10px rgba(102, 126, 234, 0.2),
                    0 0 0 3px rgba(102, 126, 234, 0.4);
    }

    .language-dropdown-menu {
        background: linear-gradient(145deg, #1e293b 0%, #334155 100%);
        border-color: rgba(102, 126, 234, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4),
                    0 10px 20px rgba(0, 0, 0, 0.3),
                    0 0 0 1px rgba(102, 126, 234, 0.2);
    }

    .language-dropdown-menu::before {
        background: linear-gradient(145deg, #1e293b 0%, #334155 100%);
        border-color: rgba(102, 126, 234, 0.2);
        box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.2);
    }

    .lang-btn-new {
        color: #e2e8f0;
    }

    .lang-btn-new:hover {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.1) 100%);
        color: #f1f5f9;
    }

    .lang-btn-new.active-lang-item {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .lang-btn-new.active-lang-item:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    }
}

/* تحسين للطباعة */
@media print {
    .language-top-bar-new {
        display: none !important;
    }
}

/* تحسين للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    .language-dropdown-btn,
    .language-dropdown-btn:hover,
    .language-dropdown-btn::before,
    .language-dropdown-menu,
    .lang-btn-new,
    .lang-btn-new:hover,
    .lang-btn-new::before {
        transition: none !important;
        transform: none !important;
        animation: none !important;
    }
}

/* تحسينات الأداء */
.language-dropdown-btn,
.language-dropdown-menu,
.lang-btn-new {
    will-change: transform, opacity;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

/* تأثيرات إضافية للتفاعل */
.language-dropdown-btn:active {
    transition-duration: 0.1s;
}

.lang-btn-new:active {
    transition-duration: 0.1s;
    transform: translateX(2px) scale(0.98);
}

/* تحسين النص الأمازيغي */
.tifinagh-text {
    font-family: 'Noto Sans Tifinagh', 'Tifinagh', sans-serif;
    font-size: 1.05em;
    line-height: 1.2;
}

/* تأثير النبضة للزر النشط */
.language-dropdown-btn[aria-expanded="true"] {
    animation: activeButtonPulse 2s ease-in-out infinite;
}

@keyframes activeButtonPulse {
    0%, 100% {
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1),
                    0 2px 4px rgba(0, 0, 0, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }
    50% {
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.2),
                    0 3px 6px rgba(0, 0, 0, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }
}

@media (prefers-reduced-motion: reduce) {
    .language-dropdown-btn[aria-expanded="true"] {
        animation: none !important;
    }
}

.lang-btn-simple.active:hover {
    background: white;
    color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* الأيقونات */
.lang-btn-simple i {
    font-size: 0.85rem;
    opacity: 0.9;
}

.lang-btn-simple.active i {
    opacity: 1;
}

/* النص الأمازيغي */
.lang-btn-simple .tifinagh-text {
    font-family: 'Noto Sans Tifinagh', sans-serif;
    font-size: 0.95rem;
}

/* تأثير التحميل */
.lang-btn-simple.loading {
    opacity: 0.7;
    pointer-events: none;
}

.lang-btn-simple.loading::after {
    content: '';
    width: 12px;
    height: 12px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .language-top-bar {
        padding: 0;
    }
    
    .language-top-bar .py-2 {
        padding-top: 0.5rem !important;
        padding-bottom: 0.5rem !important;
    }
    
    .lang-btn-simple {
        padding: 5px 12px;
        font-size: 0.85rem;
    }
    
    .language-switcher-simple {
        gap: 6px;
    }
}

@media (max-width: 480px) {
    .lang-btn-simple {
        padding: 4px 10px;
        font-size: 0.8rem;
    }
    
    .lang-btn-simple i {
        font-size: 0.75rem;
    }
    
    .language-switcher-simple {
        gap: 4px;
    }
}

/* تحسين للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .language-top-bar {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    }
    
    .lang-btn-simple {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.15);
    }
    
    .lang-btn-simple:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
    }
    
    .lang-btn-simple.active {
        background: rgba(255, 255, 255, 0.95);
        color: #2d3748;
    }
}

/* تحسين للطباعة */
@media print {
    .language-top-bar {
        display: none !important;
    }
}

/* تحسين إمكانية الوصول */
.lang-btn-simple:focus {
    outline: 2px solid rgba(255, 255, 255, 0.8);
    outline-offset: 2px;
}

.lang-btn-simple.active:focus {
    outline: 2px solid #667eea;
}

/* تحسين للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    .lang-btn-simple,
    .lang-btn-simple:hover,
    .lang-btn-simple.active {
        transition: none !important;
        transform: none !important;
    }
    
    .lang-btn-simple.loading::after {
        animation: none !important;
    }
}

/* تأثير النبضة للزر النشط */
.lang-btn-simple.active {
    animation: activeGlow 2s ease-in-out infinite alternate;
}

@keyframes activeGlow {
    0% {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    100% {
        box-shadow: 0 2px 12px rgba(102, 126, 234, 0.3);
    }
}

@media (prefers-reduced-motion: reduce) {
    .lang-btn-simple.active {
        animation: none !important;
    }
}

/* تحسين التباعد */
.language-top-bar .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
}

@media (min-width: 1200px) {
    .language-top-bar .container-fluid {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

/* تأثير التدرج المتحرك */
.language-top-bar {
    background-size: 200% 200%;
    animation: gradientShift 8s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@media (prefers-reduced-motion: reduce) {
    .language-top-bar {
        animation: none !important;
        background-size: 100% 100%;
    }
}
