/**
 * شريط تغيير اللغة العلوي - تصميم بسيط وبارز
 * Language Top Bar - Simple and Prominent Design
 */

/* الشريط العلوي */
.language-top-bar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: sticky;
    top: 0;
    z-index: 1050;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* حاوي أزرار اللغة */
.language-switcher-simple {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* أزرار اللغة البسيطة */
.lang-btn-simple {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    letter-spacing: 0.02em;
}

/* حالة التمرير */
.lang-btn-simple:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    color: white;
}

/* حالة النشاط */
.lang-btn-simple.active {
    background: rgba(255, 255, 255, 0.9);
    color: #667eea;
    border-color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.lang-btn-simple.active:hover {
    background: white;
    color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* الأيقونات */
.lang-btn-simple i {
    font-size: 0.85rem;
    opacity: 0.9;
}

.lang-btn-simple.active i {
    opacity: 1;
}

/* النص الأمازيغي */
.lang-btn-simple .tifinagh-text {
    font-family: 'Noto Sans Tifinagh', sans-serif;
    font-size: 0.95rem;
}

/* تأثير التحميل */
.lang-btn-simple.loading {
    opacity: 0.7;
    pointer-events: none;
}

.lang-btn-simple.loading::after {
    content: '';
    width: 12px;
    height: 12px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .language-top-bar {
        padding: 0;
    }
    
    .language-top-bar .py-2 {
        padding-top: 0.5rem !important;
        padding-bottom: 0.5rem !important;
    }
    
    .lang-btn-simple {
        padding: 5px 12px;
        font-size: 0.85rem;
    }
    
    .language-switcher-simple {
        gap: 6px;
    }
}

@media (max-width: 480px) {
    .lang-btn-simple {
        padding: 4px 10px;
        font-size: 0.8rem;
    }
    
    .lang-btn-simple i {
        font-size: 0.75rem;
    }
    
    .language-switcher-simple {
        gap: 4px;
    }
}

/* تحسين للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .language-top-bar {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    }
    
    .lang-btn-simple {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.15);
    }
    
    .lang-btn-simple:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
    }
    
    .lang-btn-simple.active {
        background: rgba(255, 255, 255, 0.95);
        color: #2d3748;
    }
}

/* تحسين للطباعة */
@media print {
    .language-top-bar {
        display: none !important;
    }
}

/* تحسين إمكانية الوصول */
.lang-btn-simple:focus {
    outline: 2px solid rgba(255, 255, 255, 0.8);
    outline-offset: 2px;
}

.lang-btn-simple.active:focus {
    outline: 2px solid #667eea;
}

/* تحسين للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    .lang-btn-simple,
    .lang-btn-simple:hover,
    .lang-btn-simple.active {
        transition: none !important;
        transform: none !important;
    }
    
    .lang-btn-simple.loading::after {
        animation: none !important;
    }
}

/* تأثير النبضة للزر النشط */
.lang-btn-simple.active {
    animation: activeGlow 2s ease-in-out infinite alternate;
}

@keyframes activeGlow {
    0% {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    100% {
        box-shadow: 0 2px 12px rgba(102, 126, 234, 0.3);
    }
}

@media (prefers-reduced-motion: reduce) {
    .lang-btn-simple.active {
        animation: none !important;
    }
}

/* تحسين التباعد */
.language-top-bar .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
}

@media (min-width: 1200px) {
    .language-top-bar .container-fluid {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

/* تأثير التدرج المتحرك */
.language-top-bar {
    background-size: 200% 200%;
    animation: gradientShift 8s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@media (prefers-reduced-motion: reduce) {
    .language-top-bar {
        animation: none !important;
        background-size: 100% 100%;
    }
}
