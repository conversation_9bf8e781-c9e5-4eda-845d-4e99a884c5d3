/**
 * شريط تغيير اللغة العلوي الجديد - تصميم أنيق ومتطور
 * New Language Top Bar - Elegant and Modern Design
 */

/* الشريط العلوي الجديد */
.language-top-bar-new {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid #e2e8f0;
    position: sticky;
    top: 0;
    z-index: 1050;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* حاوي منتقي اللغة الجديد */
.language-selector-new {
    position: relative;
    display: inline-block;
}

/* زر القائمة المنسدلة */
.language-dropdown-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 20px;
    border: 1px solid #3b82f6;
    color: #1d4ed8;
    background-color: white;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 500;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    outline: none;
}

.language-dropdown-btn:hover {
    background-color: #dbeafe;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.language-dropdown-btn:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* القائمة المنسدلة */
.language-dropdown-menu {
    position: absolute;
    right: 0;
    top: calc(100% + 8px);
    z-index: 10;
    min-width: 12rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    list-style: none;
    margin: 0;
    padding: 4px 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease-in-out;
}

.language-dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* عناصر القائمة */
.language-dropdown-menu li {
    margin: 0;
    padding: 0;
}

.lang-btn-new {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 8px 16px;
    font-size: 0.875rem;
    color: #374151;
    background: none;
    border: none;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.lang-btn-new:hover {
    background-color: #f3f4f6;
}

.lang-btn-new.active-lang-item {
    background-color: #dbeafe;
    color: #1d4ed8;
    font-weight: 600;
}

/* النص الأمازيغي */
.tifinagh-text {
    font-family: 'Noto Sans Tifinagh', sans-serif;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .language-top-bar-new {
        padding: 0;
    }

    .language-top-bar-new .py-2 {
        padding-top: 0.5rem !important;
        padding-bottom: 0.5rem !important;
    }

    .language-dropdown-btn {
        padding: 6px 16px;
        font-size: 0.8rem;
    }

    .language-dropdown-menu {
        min-width: 10rem;
    }

    .lang-btn-new {
        padding: 6px 12px;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .language-dropdown-btn {
        padding: 5px 12px;
        font-size: 0.75rem;
    }

    .language-dropdown-menu {
        min-width: 8rem;
    }

    .lang-btn-new {
        padding: 5px 10px;
        font-size: 0.75rem;
    }
}

/* تحسين للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .language-top-bar-new {
        background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        border-bottom-color: #374151;
    }

    .language-dropdown-btn {
        background-color: #374151;
        border-color: #6b7280;
        color: #e5e7eb;
    }

    .language-dropdown-btn:hover {
        background-color: #4b5563;
    }

    .language-dropdown-menu {
        background-color: #374151;
        border-color: #4b5563;
    }

    .lang-btn-new {
        color: #e5e7eb;
    }

    .lang-btn-new:hover {
        background-color: #4b5563;
    }

    .lang-btn-new.active-lang-item {
        background-color: #1e40af;
        color: #dbeafe;
    }
}

/* تحسين للطباعة */
@media print {
    .language-top-bar-new {
        display: none !important;
    }
}

/* تحسين للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    .language-dropdown-btn,
    .language-dropdown-btn:hover,
    .language-dropdown-menu,
    .lang-btn-new,
    .lang-btn-new:hover {
        transition: none !important;
        transform: none !important;
    }
}

.lang-btn-simple.active:hover {
    background: white;
    color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* الأيقونات */
.lang-btn-simple i {
    font-size: 0.85rem;
    opacity: 0.9;
}

.lang-btn-simple.active i {
    opacity: 1;
}

/* النص الأمازيغي */
.lang-btn-simple .tifinagh-text {
    font-family: 'Noto Sans Tifinagh', sans-serif;
    font-size: 0.95rem;
}

/* تأثير التحميل */
.lang-btn-simple.loading {
    opacity: 0.7;
    pointer-events: none;
}

.lang-btn-simple.loading::after {
    content: '';
    width: 12px;
    height: 12px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .language-top-bar {
        padding: 0;
    }
    
    .language-top-bar .py-2 {
        padding-top: 0.5rem !important;
        padding-bottom: 0.5rem !important;
    }
    
    .lang-btn-simple {
        padding: 5px 12px;
        font-size: 0.85rem;
    }
    
    .language-switcher-simple {
        gap: 6px;
    }
}

@media (max-width: 480px) {
    .lang-btn-simple {
        padding: 4px 10px;
        font-size: 0.8rem;
    }
    
    .lang-btn-simple i {
        font-size: 0.75rem;
    }
    
    .language-switcher-simple {
        gap: 4px;
    }
}

/* تحسين للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .language-top-bar {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    }
    
    .lang-btn-simple {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.15);
    }
    
    .lang-btn-simple:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
    }
    
    .lang-btn-simple.active {
        background: rgba(255, 255, 255, 0.95);
        color: #2d3748;
    }
}

/* تحسين للطباعة */
@media print {
    .language-top-bar {
        display: none !important;
    }
}

/* تحسين إمكانية الوصول */
.lang-btn-simple:focus {
    outline: 2px solid rgba(255, 255, 255, 0.8);
    outline-offset: 2px;
}

.lang-btn-simple.active:focus {
    outline: 2px solid #667eea;
}

/* تحسين للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    .lang-btn-simple,
    .lang-btn-simple:hover,
    .lang-btn-simple.active {
        transition: none !important;
        transform: none !important;
    }
    
    .lang-btn-simple.loading::after {
        animation: none !important;
    }
}

/* تأثير النبضة للزر النشط */
.lang-btn-simple.active {
    animation: activeGlow 2s ease-in-out infinite alternate;
}

@keyframes activeGlow {
    0% {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    100% {
        box-shadow: 0 2px 12px rgba(102, 126, 234, 0.3);
    }
}

@media (prefers-reduced-motion: reduce) {
    .lang-btn-simple.active {
        animation: none !important;
    }
}

/* تحسين التباعد */
.language-top-bar .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
}

@media (min-width: 1200px) {
    .language-top-bar .container-fluid {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

/* تأثير التدرج المتحرك */
.language-top-bar {
    background-size: 200% 200%;
    animation: gradientShift 8s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@media (prefers-reduced-motion: reduce) {
    .language-top-bar {
        animation: none !important;
        background-size: 100% 100%;
    }
}
